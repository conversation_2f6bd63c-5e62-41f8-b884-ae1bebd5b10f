{"name": "realtime-collaborative-editor-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tiptap/extension-collaboration": "^2.1.13", "@tiptap/extension-collaboration-cursor": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "js-base64": "^3.7.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "y-websocket": "^1.5.0", "yjs": "^13.6.27"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8"}}