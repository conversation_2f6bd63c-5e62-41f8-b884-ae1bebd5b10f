# Development Dockerfile for React client with Vite hot reloading
FROM node:22.7.0-alpine

# Set working directory in container
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files first for better caching
COPY package*.json ./

# Install ALL dependencies (including devDependencies for development)
# Use npm install instead of npm ci for development to handle package updates better
RUN npm install && npm cache clean --force

# Change ownership of app directory to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose Vite dev server port
EXPOSE 3001

# Health check for Vite dev server
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3001/ || exit 1

# Start Vite dev server with hot reloading
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
