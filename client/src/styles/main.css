/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  color: #1f2937;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app {
  min-height: 100vh;
  background: transparent;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
}

.header h1 {
  font-size: 48px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  letter-spacing: -0.02em;
}

.header p {
  font-size: 21px;
  color: #86868b;
  margin: 0;
  font-weight: 400;
}

.main {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.status-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  text-align: center;
  max-width: 500px;
}

.status-card h2 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.status-card p {
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

/* Authentication Styles */
.auth-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  margin-top:50px;
}

.auth-card {
  background: white;
  padding: 48px;
  border-radius: 18px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 500px;
  width: 100%;
  border: 1px solid #d2d2d7;
}

.auth-card h2 {
  color: #1d1d1f;
  margin: 0 0 8px 0;
  text-align: center;
  font-size: 32px;
  font-weight: 600;
}

.auth-card > p {
  color: #86868b;
  text-align: center;
  margin: 0 0 32px 0;
  font-size: 17px;
}

.auth-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1d1d1f;
  font-size: 17px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 16px;
  border: 1px solid #d2d2d7;
  border-radius: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007aff;
}

.form-group input:disabled,
.form-group textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f8f8f8;
}

.btn {
  display: inline-block;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 17px;
}

.btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.btn-primary {
  background: #007aff;
  color: white;
  width: 100%;
}

.btn-primary:hover:not(:disabled) {
  background: #0056cc;
}

.btn-secondary {
  background: #f2f2f7;
  color: #1d1d1f;
  width: 100%;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e5ea;
}

.btn-test {
  background: white;
  color: #1d1d1f;
  border: 1px solid #d2d2d7;
  padding: 12px 20px;
  margin: 6px;
  text-align: center;
  min-width: 120px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 15px;
}

.btn-test:hover:not(:disabled) {
  background: #f2f2f7;
  border-color: #007aff;
}

.btn-test.selected {
  background: #007aff;
  color: white;
  border-color: #007aff;
}

.btn-test small {
  display: block;
  font-size: 0.75rem;
  opacity: 0.8;
}

.test-users-section {
  border-top: 1px solid #d2d2d7;
  padding-top: 24px;
  margin-bottom: 24px;
}

.test-users-section h3 {
  color: #1d1d1f;
  margin: 0 0 8px 0;
  font-size: 21px;
  font-weight: 600;
}

.test-users-section p {
  color: #86868b;
  margin: 0 0 16px 0;
  font-size: 17px;
}

.test-users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
}

.info-section {
  border-top: 1px solid #d2d2d7;
  padding-top: 16px;
}

.info-section h4 {
  color: #1d1d1f;
  margin: 0 0 8px 0;
  font-size: 19px;
  font-weight: 600;
}

.info-section ol {
  color: #86868b;
  padding-left: 24px;
  font-size: 15px;
}

.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #feb2b2;
}

/* Editor Section Styles */
.editor-section {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
}

.editor-card {
  background: white;
  padding: 0;
  border-radius: 18px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  border: 1px solid #d2d2d7;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f2f2f7;
  padding: 20px 24px;
  border-bottom: 1px solid #d2d2d7;
}

.header-left h2 {
  color: #1d1d1f;
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  color: #86868b;
  margin: 0;
  font-size: 15px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1d1d1f;
  font-weight: 500;
  font-size: 15px;
}

.user-icon {
  font-size: 16px;
  color: #86868b;
}

.user-name {
  color: #1d1d1f;
}

.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}

.editor-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 1rem;
  min-height: 600px;
}

.editor-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.editor-sidebar {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 1rem;
}

/* Editor Container */
.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #4a5568;
}

.editor-loading h3 {
  margin-bottom: 0.5rem;
}

/* Editor Toolbar */
.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.toolbar-section {
  display: flex;
  gap: 0.25rem;
  padding-right: 0.5rem;
  border-right: 1px solid #e2e8f0;
}

.toolbar-section:last-child {
  border-right: none;
}

.toolbar-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.toolbar-btn:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.toolbar-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Editor Content */
.editor-content-wrapper {
  flex: 1;
  overflow-y: auto;
}

.editor-content {
  height: 100%;
}

.tiptap-editor {
  padding: 1.5rem;
  min-height: 400px;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: #2d3748;
}

.tiptap-editor h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
}

.tiptap-editor h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
}

.tiptap-editor p {
  margin: 0.5rem 0;
}

.tiptap-editor ul, .tiptap-editor ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.tiptap-editor blockquote {
  border-left: 4px solid #667eea;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #4a5568;
}

.tiptap-editor pre {
  background: #f7fafc;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.tiptap-editor code {
  background: #edf2f7;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* Duplicate removed */

/* Connection Status Styles */
.connection-status {
  font-size: 0.9rem;
}

.status-header {
  margin-bottom: 1rem;
}

.status-header h3 {
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.status-header .status-indicator {
  font-weight: 600;
}

.document-info {
  background: #f7fafc;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.document-info p {
  margin: 0.25rem 0;
  color: #4a5568;
}

.users-section {
  margin-bottom: 1rem;
}

.users-section h4 {
  color: #2d3748;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  background: #f7fafc;
  margin-bottom: 0.5rem;
}

.user-item.current-user {
  background: #e6fffa;
  border: 1px solid #81e6d9;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

/* Duplicate removed */

.no-users {
  color: #4a5568;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.connection-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.connection-details h4 {
  color: #2d3748;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

/* Duplicate removed */

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.detail-label {
  font-weight: 500;
  color: #4a5568;
}

.detail-value {
  color: #2d3748;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

/* Editor Error - Professional Design */
.editor-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  padding: 20px 24px;
  border-radius: 12px;
  margin-top: 24px;
  border: 1px solid #fecaca;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
}

.editor-error h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.editor-error p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive Design - Enhanced for Professional Theme */
@media (max-width: 1024px) {
  .editor-section {
    max-width: 1200px;
  }

  .editor-layout {
    grid-template-columns: 1fr 300px;
  }
}

@media (max-width: 768px) {
  .editor-section {
    padding: 16px;
    max-width: 100%;
  }

  .editor-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .editor-sidebar {
    order: -1;
  }

  .editor-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 20px 24px;
  }

  .header-right {
    justify-content: center;
    width: 100%;
  }

  .user-info {
    justify-content: center;
  }

  .btn-small {
    width: auto;
    min-width: 120px;
  }
}

/* Editor Section Styles - Professional Blue & White Theme */
.editor-section {
  max-width: 1600px;
  margin: 0 auto;
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.95) 0%, rgba(59, 130, 246, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 24px 32px;
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(37, 99, 235, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.1) inset,
    0 -1px 0 rgba(0, 0, 0, 0.1) inset;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.editor-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.header-left h2 {
  color: #ffffff;
  margin: 0 0 6px 0;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: -0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-left p {
  color: #ffffff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-left p strong {
  color: #ffffff;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.15);
  padding: 2px 8px;
  border-radius: 4px;
  margin-left: 4px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-shrink: 0;
  height: 100%;
}

.editor-header .header-right .user-info {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  padding: 10px 16px !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  white-space: nowrap !important;
  min-width: auto !important;
  width: auto !important;
  height: 40px !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}

.editor-header .header-right .user-icon {
  font-size: 18px;
  color: #ffffff !important;
  opacity: 1;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
}

.header-right .logout-icon {
  font-size: 16px;
  margin-right: 6px;
  color: #2563eb;
}

.editor-header .header-right .user-name {
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  line-height: 1.3;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  letter-spacing: 0.01em;
}

.header-right .btn-small {
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  background: #ffffff;
  color: #2563eb;
  border: none;
  border-radius: 10px;
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-right .btn-small:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  color: #1d4ed8;
}

.editor-layout {
  display: grid;
  grid-template-columns: 1fr 360px;
  gap: 24px;
  min-height: 600px;
}

.editor-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.editor-sidebar {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 0;
  border: 1px solid #e2e8f0;
  height: fit-content;
  overflow: hidden;
}

/* Editor Container - Enhanced Professional Design */
.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.editor-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #64748b;
  background: #f8fafc;
  border-radius: 8px;
  margin: 24px;
  border: 2px dashed #cbd5e1;
}

.editor-loading h3 {
  margin: 0 0 8px 0;
  color: #1e40af;
  font-size: 18px;
  font-weight: 600;
}

.editor-loading p {
  margin: 4px 0;
  color: #64748b;
  font-size: 14px;
  text-align: center;
  max-width: 300px;
}

.editor-loading small {
  color: #94a3b8;
  font-size: 12px;
}

/* Editor Toolbar - Professional Blue Theme */
.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  align-items: center;
}

.toolbar-section {
  display: flex;
  gap: 4px;
  padding-right: 12px;
  border-right: 1px solid #cbd5e1;
  align-items: center;
}

.toolbar-section:last-child {
  border-right: none;
}

.toolbar-btn {
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  color: #374151;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #1e40af;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.toolbar-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.toolbar-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #f9fafb;
  color: #9ca3af;
}

/* Editor Content - Enhanced Professional Design */
.editor-content-wrapper {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.editor-content {
  height: 100%;
  background: white;
}

.tiptap-editor {
  padding: 32px;
  min-height: 500px;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.7;
  color: #1f2937;
  background: white;
  border: none;
}

.tiptap-editor:focus {
  outline: none;
  background: white;
}

.tiptap-editor h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
}

.tiptap-editor h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
}

.tiptap-editor p {
  margin: 0.5rem 0;
}

.tiptap-editor ul, .tiptap-editor ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.tiptap-editor blockquote {
  border-left: 4px solid #667eea;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #4a5568;
}

.tiptap-editor pre {
  background: #f7fafc;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.tiptap-editor code {
  background: #edf2f7;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* Editor Status */
.editor-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f2f2f7;
  border-top: 1px solid #d2d2d7;
  font-size: 14px;
}

.status-indicator, .connection-indicator {
  color: #86868b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator svg, .connection-indicator svg {
  width: 14px;
  height: 14px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 12px;
}

.status-text {
  font-size: 14px;
}

/* Connection Status Component - Professional Blue Theme */
.connection-status {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.status-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #2563eb;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 16px;
  color: #2563eb;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #065f46;
  background: #d1fae5;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #6ee7b7;
}

/* Document Info Section */
.document-info {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.document-info h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.info-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.info-value {
  color: #1f2937;
  font-weight: 600;
  font-size: 13px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, monospace;
}

.users-section {
  margin: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.users-section h4 {
  margin: 0 0 16px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.user-item:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.user-item.current-user {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  box-shadow: 0 2px 12px rgba(37, 99, 235, 0.15);
}

.user-item.current-user:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.current-user-avatar {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.avatar-icon {
  font-size: 16px;
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.user-name {
  color: #1f2937;
  font-weight: 600;
  font-size: 13px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-item.current-user .user-name {
  color: #1e40af;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.user-item.current-user .user-status {
  color: #1e40af;
}

.status-icon-small {
  font-size: 12px;
  color: #10b981;
}

.connection-details {
  padding: 16px 20px;
}

.connection-details h4 {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 12px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 12px;
  gap: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.detail-label {
  font-weight: 600;
  color: #374151;
  flex-shrink: 0;
  min-width: 90px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.detail-value {
  color: #4b5563;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 11px;
  word-break: break-all;
  text-align: right;
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.error-message {
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 13px;
  margin: 16px 20px;
  font-weight: 500;
}

/* ===== COLLABORATION IMPROVEMENTS ===== */

/* Collaboration Cursors - TipTap Default Style */
.collaboration-cursor__caret {
  border-left: 1px solid #0d0d0d;
  border-right: 1px solid #0d0d0d;
  margin-left: -1px;
  margin-right: -1px;
  pointer-events: none;
  position: relative;
  word-break: normal;
}

.collaboration-cursor__label {
  border-radius: 3px 3px 3px 0;
  color: #0d0d0d;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  left: -1px;
  line-height: normal;
  padding: 0.1rem 0.3rem;
  position: absolute;
  top: -1.4em;
  user-select: none;
  white-space: nowrap;
  pointer-events: none;
}

/* Text Selection Styling */
.ProseMirror ::selection {
  background-color: rgba(102, 126, 234, 0.2);
}

/* ===== IMPROVED UI DESIGN ===== */

/* Better Editor Container */
.editor-content-wrapper {
  border: none;
  background: white;
  overflow: hidden;
}

/* Removed empty focus-within rule */

/* Enhanced Editor Content */
.tiptap-editor {
  padding: 32px;
  min-height: 400px;
  max-width: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 17px;
  line-height: 1.6;
  color: #1d1d1f;
  position: relative;
}

.tiptap-editor:focus {
  outline: none;
}

/* Read-only mode styles */
.tiptap-editor.read-only {
  background-color: #f8f9fa;
  cursor: default;
  opacity: 0.8;
  border: 2px dashed #dee2e6;
}

.tiptap-editor.read-only::before {
  content: "🔒 Read-Only Mode";
  position: absolute;
  top: 8px;
  right: 16px;
  background: #ff6b6b;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

/* Better Typography */
.tiptap-editor h1 {
  font-size: 32px;
  font-weight: 700;
  margin: 32px 0 16px 0;
  color: #1d1d1f;
  line-height: 1.2;
}

.tiptap-editor h2 {
  font-size: 28px;
  font-weight: 600;
  margin: 28px 0 14px 0;
  color: #1d1d1f;
  line-height: 1.3;
}

.tiptap-editor p {
  margin: 0 0 16px 0;
}

/* Enhanced Blockquotes */
.tiptap-editor blockquote {
  border-left: 4px solid #007aff;
  background: #f2f2f7;
  padding: 16px 20px;
  margin: 16px 0;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #86868b;
}

/* Better Code Blocks */
.tiptap-editor pre {
  background: #1d1d1f;
  color: #f2f2f7;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.tiptap-editor code {
  background: #f2f2f7;
  color: #1d1d1f;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  font-weight: 500;
}

/* Enhanced Toolbar */
.editor-toolbar {
  background: #f2f2f7;
  padding: 16px 20px;
  border-bottom: 1px solid #d2d2d7;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.toolbar-btn {
  background: white;
  border: 1px solid #d2d2d7;
  color: #1d1d1f;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 16px;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn svg {
  width: 16px;
  height: 16px;
}

.toolbar-btn:hover:not(:disabled) {
  background: #f2f2f7;
  border-color: #007aff;
}

.toolbar-btn.active {
  background: #007aff;
  color: white;
  border-color: #007aff;
}
