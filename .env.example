# Environment Configuration for WebSocket Collaboration Server
# Requires Node.js 22.7.0+ and Redis 7.4+

# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_ISSUER=collaboration-server
JWT_AUDIENCE=collaboration-clients

# Redis Configuration (Redis 7.4+ recommended)
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=collab:

# CORS Configuration
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# YJS Configuration
YJS_PERSISTENCE=false
YJS_GC_ENABLED=true
YJS_CLEANUP_INTERVAL=300000
YJS_MAX_IDLE_TIME=1800000

# WebSocket Configuration
WS_PING_TIMEOUT=60000
WS_MAX_CONNECTIONS=1000

# Authentication Test Mode (bypasses JWT signature verification for testing)
AUTH_TEST_MODE=false

# Document Access Control
DEFAULT_DOCUMENT_ACCESS=true

# Performance Optimizations (RECOMMENDED for production)
ENABLE_OPTIMIZATIONS=true

# Memory Management Configuration
MAX_MEMORY_USAGE=1073741824
MEMORY_GC_THRESHOLD=0.85
MEMORY_GC_INTERVAL=30000
DOCUMENT_CACHE_SIZE=50
DOCUMENT_HISTORY_LIMIT=25

# Node.js Memory Optimization (for production)
# NODE_OPTIONS=--max-old-space-size=2048 --expose-gc

# Debouncing Configuration (Performance Optimization)
DEBOUNCE_ENABLED=true
DEBOUNCE_DELAY=300
DEBOUNCE_MAX_DELAY=1000
DEBOUNCE_MIN_DELAY=50

# Large Document Optimization
LARGE_DOC_THRESHOLD=1048576
LARGE_DOC_DELAY=500
LARGE_DOC_MAX_DELAY=2000

# Batch Processing
BATCH_ENABLED=true
BATCH_SIZE=10
BATCH_TIMEOUT=100

# Connection Scaling
CONNECTION_SCALING=true
BASE_CONNECTION_COUNT=1
SCALING_FACTOR=1.2

# Production Settings (uncomment for production)
# NODE_ENV=production
# LOG_LEVEL=warn
