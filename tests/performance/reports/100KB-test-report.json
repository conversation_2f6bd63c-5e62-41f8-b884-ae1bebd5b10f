{"config": {"size": 102400, "label": "100KB", "users": 5, "duration": 60000, "documentType": "report", "testType": "standard_load"}, "success": true, "totalTime": 68505, "documentSummary": {"size": 93197, "sizeFormatted": "0.09MB", "contentTypes": {"paragraphs": 628, "tables": 19, "lists": 32, "headings": 6}, "estimatedWords": 15532, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752404460911, "endTime": 1752404529415, "duration": 68504, "testType": "standard_load", "configuration": {"testType": "standard_load", "documentSize": 102400, "userCount": 5, "duration": 60000, "documentType": "report"}}, "summary": {"duration": "1.1m", "durationMs": 68504, "totalUsers": 5, "successfulConnections": 5, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "11.72 MB", "peakBytes": 12292064, "average": "10.55 MB", "averageBytes": 11067478.76923077, "maxUsagePercent": "87.6%"}, "network": {"peakThroughput": "0 msg/s", "totalMessages": 0, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 5, "activeUsers": 5, "averageConnectionTime": "41.2ms", "averageConnectionTimeMs": 41.2, "totalEdits": 261, "averageEditsPerUser": 52.2, "userRoles": {"admin": 2, "editor": 1, "reviewer": 1, "contributor": 1}}, "documentMetrics": {"totalDocuments": 1, "totalOperations": 261, "averageOperationsPerDocument": 261}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T11:02:09.416Z", "generatedAt": "2025-07-13T11:02:11.437Z", "testSuite": "Professional Large Document Performance Test", "version": "2.0.0"}