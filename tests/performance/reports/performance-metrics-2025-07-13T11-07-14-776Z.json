{"testSession": {"startTime": 1752404697939, "endTime": 1752404832737, "duration": 134798, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134798, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "31.32 MB", "peakBytes": 32840712, "average": "20.19 MB", "averageBytes": 21170497.627118643, "maxUsagePercent": "92.6%"}, "network": {"peakThroughput": "499 msg/s", "totalMessages": 81430, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "27.5ms", "averageConnectionTimeMs": 27.5, "totalEdits": 1775, "averageEditsPerUser": 177.5, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2674, "averageOperationsPerDocument": 891.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}