{"testSuite": "Professional Large Document Performance Test Suite", "version": "2.0.0", "generatedAt": "2025-07-13T11:07:14.777Z", "serverUrl": "ws://localhost:3000", "configuration": {"outputDir": "/Users/<USER>/Desktop/realtime_yjs_server/tests/performance/reports", "verbose": true, "saveDetailedLogs": true, "serverUrl": "ws://localhost:3000"}, "summary": {"totalTests": 3, "successfulTests": 3, "failedTests": 0, "successRate": "100.00"}, "testResults": [{"config": {"size": 102400, "label": "100KB", "users": 5, "duration": 60000, "documentType": "report", "testType": "standard_load"}, "success": true, "totalTime": 68505, "documentSummary": {"size": 93197, "sizeFormatted": "0.09MB", "contentTypes": {"paragraphs": 628, "tables": 19, "lists": 32, "headings": 6}, "estimatedWords": 15532, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752404697939, "endTime": 1752404832737, "duration": 134798, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "1.1m", "durationMs": 68504, "totalUsers": 5, "successfulConnections": 5, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "11.72 MB", "peakBytes": 12292064, "average": "10.55 MB", "averageBytes": 11067478.76923077, "maxUsagePercent": "87.6%"}, "network": {"peakThroughput": "0 msg/s", "totalMessages": 0, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 5, "activeUsers": 5, "averageConnectionTime": "41.2ms", "averageConnectionTimeMs": 41.2, "totalEdits": 261, "averageEditsPerUser": 52.2, "userRoles": {"admin": 2, "editor": 1, "reviewer": 1, "contributor": 1}}, "documentMetrics": {"totalDocuments": 1, "totalOperations": 261, "averageOperationsPerDocument": 261}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T11:02:09.416Z"}, {"config": {"size": 1572864, "label": "1.5MB", "users": 10, "duration": 90000, "documentType": "proposal", "testType": "medium_load"}, "success": true, "totalTime": 104453, "documentSummary": {"size": 641615, "sizeFormatted": "0.61MB", "contentTypes": {"paragraphs": 4122, "tables": 102, "lists": 286, "headings": 6}, "estimatedWords": 106935, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752404697939, "endTime": 1752404832737, "duration": 134798, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "1.7m", "durationMs": 104453, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "23.56 MB", "peakBytes": 24703400, "average": "16.84 MB", "averageBytes": 17653542.303030305, "maxUsagePercent": "92.6%"}, "network": {"peakThroughput": "166 msg/s", "totalMessages": 16560, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "38.6ms", "averageConnectionTimeMs": 38.6, "totalEdits": 638, "averageEditsPerUser": 63.8, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 2, "totalOperations": 899, "averageOperationsPerDocument": 449.5}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T11:04:25.918Z"}, {"config": {"size": 2097152, "label": "2MB", "users": 10, "duration": 120000, "documentType": "specification", "testType": "heavy_load"}, "success": true, "totalTime": 134798, "documentSummary": {"size": 693848, "sizeFormatted": "0.66MB", "contentTypes": {"paragraphs": 4689, "tables": 135, "lists": 277, "headings": 5}, "estimatedWords": 115641, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752404697939, "endTime": 1752404832737, "duration": 134798, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134798, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "31.32 MB", "peakBytes": 32840712, "average": "20.19 MB", "averageBytes": 21170497.627118643, "maxUsagePercent": "92.6%"}, "network": {"peakThroughput": "499 msg/s", "totalMessages": 81430, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "27.5ms", "averageConnectionTimeMs": 27.5, "totalEdits": 1775, "averageEditsPerUser": 177.5, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2674, "averageOperationsPerDocument": 891.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T11:07:12.739Z"}], "performanceCollectorReport": {"testSession": {"startTime": 1752404697939, "endTime": 1752404832737, "duration": 134798, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134798, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "31.32 MB", "peakBytes": 32840712, "average": "20.19 MB", "averageBytes": 21170497.627118643, "maxUsagePercent": "92.6%"}, "network": {"peakThroughput": "499 msg/s", "totalMessages": 81430, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "27.5ms", "averageConnectionTimeMs": 27.5, "totalEdits": 1775, "averageEditsPerUser": 177.5, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2674, "averageOperationsPerDocument": 891.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}}