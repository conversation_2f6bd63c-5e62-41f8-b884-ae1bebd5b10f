const rateLimit = require('express-rate-limit');

class MiddlewareFactory {
  constructor(logger, authenticationHandler) {
    this.logger = logger;
    this.authenticationHandler = authenticationHandler;
    this.middlewareCache = new Map();
  }

  createAuthMiddleware(config = {}) {
    const middlewareId = `auth-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const middleware = async (req, res, next) => {
      try {
        if (!config.required) {
          return next();
        }

        const token = req.headers.authorization;
        if (!token) {
          return res.status(401).json({ 
            error: 'Authentication required',
            message: 'No authorization token provided'
          });
        }

        const authMiddleware = this.authenticationHandler.getAuthMiddleware();
        const userInfo = await authMiddleware.validateToken(token);
        
        if (!userInfo) {
          return res.status(401).json({ 
            error: 'Invalid token',
            message: 'Authentication token is invalid or expired'
          });
        }

        req.user = userInfo;
        next();
      } catch (error) {
        this.logger.error('Authentication middleware error', error, {
          service: 'middleware-factory'
        });
        res.status(500).json({ 
          error: 'Authentication error',
          message: 'Internal authentication error'
        });
      }
    };

    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createRateLimitMiddleware(config = {}) {
    const middlewareId = `rateLimit-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const options = {
      windowMs: config.windowMs || 900000, // 15 minutes
      max: config.max || 100,
      message: config.message || {
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.'
      },
      standardHeaders: config.standardHeaders !== false,
      legacyHeaders: config.legacyHeaders === true,
      skip: config.skip || (() => false),
      keyGenerator: config.keyGenerator || ((req) => req.ip),
      onLimitReached: (req, res, options) => {
        this.logger.warn('Rate limit exceeded', {
          ip: req.ip,
          path: req.path,
          method: req.method,
          limit: options.max,
          windowMs: options.windowMs,
          service: 'middleware-factory'
        });
      }
    };

    const middleware = rateLimit(options);
    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createValidationMiddleware(config = {}) {
    const middlewareId = `validation-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const middleware = (req, res, next) => {
      try {
        const errors = [];

        // Validate required parameters
        if (config.requiredParams) {
          for (const param of config.requiredParams) {
            if (!req.params[param]) {
              errors.push(`Missing required parameter: ${param}`);
            }
          }
        }

        // Validate required query parameters
        if (config.requiredQuery) {
          for (const query of config.requiredQuery) {
            if (!req.query[query]) {
              errors.push(`Missing required query parameter: ${query}`);
            }
          }
        }

        // Validate required body fields
        if (config.requiredBody) {
          for (const field of config.requiredBody) {
            if (!req.body || !req.body[field]) {
              errors.push(`Missing required body field: ${field}`);
            }
          }
        }

        // Custom validation function
        if (config.customValidator && typeof config.customValidator === 'function') {
          const customErrors = config.customValidator(req);
          if (customErrors && Array.isArray(customErrors)) {
            errors.push(...customErrors);
          }
        }

        if (errors.length > 0) {
          return res.status(400).json({
            error: 'Validation failed',
            message: 'Request validation failed',
            details: errors
          });
        }

        next();
      } catch (error) {
        this.logger.error('Validation middleware error', error, {
          service: 'middleware-factory'
        });
        res.status(500).json({
          error: 'Validation error',
          message: 'Internal validation error'
        });
      }
    };

    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createLoggingMiddleware(config = {}) {
    const middlewareId = `logging-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const middleware = (req, res, next) => {
      const startTime = Date.now();
      
      // Log request
      if (config.logRequests !== false) {
        this.logger.info('HTTP Request', {
          method: req.method,
          path: req.path,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
          service: 'middleware-factory'
        });
      }

      // Override res.end to log response
      const originalEnd = res.end;
      res.end = function(...args) {
        const duration = Date.now() - startTime;
        
        if (config.logResponses !== false) {
          this.logger.info('HTTP Response', {
            method: req.method,
            path: req.path,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            service: 'middleware-factory'
          });
        }

        originalEnd.apply(res, args);
      }.bind(this);

      next();
    };

    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createCorsMiddleware(config = {}) {
    const middlewareId = `cors-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const middleware = (req, res, next) => {
      const origin = config.origin || '*';
      const methods = config.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
      const headers = config.headers || ['Content-Type', 'Authorization'];

      res.header('Access-Control-Allow-Origin', origin);
      res.header('Access-Control-Allow-Methods', methods.join(', '));
      res.header('Access-Control-Allow-Headers', headers.join(', '));
      
      if (config.credentials) {
        res.header('Access-Control-Allow-Credentials', 'true');
      }

      if (req.method === 'OPTIONS') {
        return res.sendStatus(200);
      }

      next();
    };

    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createErrorHandlingMiddleware(config = {}) {
    const middlewareId = `errorHandling-${JSON.stringify(config)}`;
    
    if (this.middlewareCache.has(middlewareId)) {
      return this.middlewareCache.get(middlewareId);
    }

    const middleware = (error, req, res, next) => {
      this.logger.error('Route error', error, {
        method: req.method,
        path: req.path,
        ip: req.ip,
        service: 'middleware-factory'
      });

      if (res.headersSent) {
        return next(error);
      }

      const statusCode = error.statusCode || error.status || 500;
      const message = config.exposeErrors !== false ? error.message : 'Internal server error';

      res.status(statusCode).json({
        error: 'Request failed',
        message: message,
        timestamp: new Date().toISOString()
      });
    };

    this.middlewareCache.set(middlewareId, middleware);
    return middleware;
  }

  createCustomMiddleware(name, middlewareFunction) {
    if (typeof middlewareFunction !== 'function') {
      throw new Error('Middleware must be a function');
    }

    this.middlewareCache.set(name, middlewareFunction);
    return middlewareFunction;
  }

  getMiddleware(name) {
    return this.middlewareCache.get(name);
  }

  clearCache() {
    this.middlewareCache.clear();
    this.logger.info('Middleware cache cleared', {
      service: 'middleware-factory'
    });
  }

  getStats() {
    return {
      cachedMiddleware: this.middlewareCache.size,
      middlewareTypes: Array.from(this.middlewareCache.keys()),
      timestamp: new Date()
    };
  }
}

module.exports = MiddlewareFactory;
