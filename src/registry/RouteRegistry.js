const express = require('express');

class RouteRegistry {
  constructor(logger) {
    this.logger = logger;
    this.routes = new Map();
    this.middleware = new Map();
    this.routers = new Map();
  }

  registerRoute(routeId, config, handler) {
    try {
      if (this.routes.has(routeId)) {
        this.logger.warn(`Route ${routeId} already exists, overwriting`, {
          service: 'route-registry'
        });
      }

      const routeInfo = {
        id: routeId,
        config: config,
        handler: handler,
        enabled: config.enabled !== false,
        registeredAt: new Date(),
        middleware: config.middleware || []
      };

      this.routes.set(routeId, routeInfo);

      this.logger.debug(`Route registered: ${routeId}`, {
        path: config.path,
        method: config.method,
        enabled: routeInfo.enabled,
        service: 'route-registry'
      });

      return routeInfo;
    } catch (error) {
      this.logger.error(`Failed to register route ${routeId}`, error, {
        service: 'route-registry'
      });
      throw error;
    }
  }

  unregisterRoute(routeId) {
    try {
      const route = this.routes.get(routeId);
      if (!route) {
        this.logger.warn(`Route ${routeId} not found for unregistration`, {
          service: 'route-registry'
        });
        return false;
      }

      this.routes.delete(routeId);

      this.logger.debug(`Route unregistered: ${routeId}`, {
        service: 'route-registry'
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to unregister route ${routeId}`, error, {
        service: 'route-registry'
      });
      throw error;
    }
  }

  enableRoute(routeId) {
    const route = this.routes.get(routeId);
    if (route) {
      route.enabled = true;
      route.config.enabled = true;
      this.logger.info(`Route enabled: ${routeId}`, {
        service: 'route-registry'
      });
      return true;
    }
    return false;
  }

  disableRoute(routeId) {
    const route = this.routes.get(routeId);
    if (route) {
      route.enabled = false;
      route.config.enabled = false;
      this.logger.info(`Route disabled: ${routeId}`, {
        service: 'route-registry'
      });
      return true;
    }
    return false;
  }

  getRoute(routeId) {
    return this.routes.get(routeId);
  }

  getAllRoutes() {
    return Array.from(this.routes.values());
  }

  getEnabledRoutes() {
    return Array.from(this.routes.values()).filter(route => route.enabled);
  }

  getDisabledRoutes() {
    return Array.from(this.routes.values()).filter(route => !route.enabled);
  }

  registerMiddleware(middlewareId, middleware) {
    try {
      this.middleware.set(middlewareId, {
        id: middlewareId,
        middleware: middleware,
        registeredAt: new Date()
      });

      this.logger.debug(`Middleware registered: ${middlewareId}`, {
        service: 'route-registry'
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to register middleware ${middlewareId}`, error, {
        service: 'route-registry'
      });
      throw error;
    }
  }

  getMiddleware(middlewareId) {
    const middlewareInfo = this.middleware.get(middlewareId);
    return middlewareInfo ? middlewareInfo.middleware : null;
  }

  createRouter(routerId, routes = []) {
    try {
      const router = express.Router();
      
      // Apply routes to router
      for (const routeId of routes) {
        const route = this.routes.get(routeId);
        if (route && route.enabled) {
          this.applyRouteToRouter(router, route);
        }
      }

      this.routers.set(routerId, {
        id: routerId,
        router: router,
        routes: routes,
        createdAt: new Date()
      });

      this.logger.debug(`Router created: ${routerId}`, {
        routeCount: routes.length,
        service: 'route-registry'
      });

      return router;
    } catch (error) {
      this.logger.error(`Failed to create router ${routerId}`, error, {
        service: 'route-registry'
      });
      throw error;
    }
  }

  applyRouteToRouter(router, route) {
    try {
      const { config, handler } = route;
      const method = (config.method || 'GET').toLowerCase();
      const path = config.path;

      // Apply middleware first
      const middlewareStack = [];
      
      if (config.middleware && Array.isArray(config.middleware)) {
        for (const middlewareId of config.middleware) {
          const middleware = this.getMiddleware(middlewareId);
          if (middleware) {
            middlewareStack.push(middleware);
          }
        }
      }

      // Apply route with middleware
      if (middlewareStack.length > 0) {
        router[method](path, ...middlewareStack, handler);
      } else {
        router[method](path, handler);
      }

      this.logger.debug(`Route applied to router`, {
        method: method.toUpperCase(),
        path: path,
        middlewareCount: middlewareStack.length,
        service: 'route-registry'
      });

    } catch (error) {
      this.logger.error(`Failed to apply route to router`, error, {
        routeId: route.id,
        service: 'route-registry'
      });
      throw error;
    }
  }

  updateRouter(routerId) {
    try {
      const routerInfo = this.routers.get(routerId);
      if (!routerInfo) {
        this.logger.warn(`Router ${routerId} not found for update`, {
          service: 'route-registry'
        });
        return null;
      }

      // Create new router with updated routes
      const newRouter = express.Router();
      
      for (const routeId of routerInfo.routes) {
        const route = this.routes.get(routeId);
        if (route && route.enabled) {
          this.applyRouteToRouter(newRouter, route);
        }
      }

      routerInfo.router = newRouter;
      routerInfo.updatedAt = new Date();

      this.logger.debug(`Router updated: ${routerId}`, {
        service: 'route-registry'
      });

      return newRouter;
    } catch (error) {
      this.logger.error(`Failed to update router ${routerId}`, error, {
        service: 'route-registry'
      });
      throw error;
    }
  }

  getRouter(routerId) {
    const routerInfo = this.routers.get(routerId);
    return routerInfo ? routerInfo.router : null;
  }

  getRegistryStats() {
    const enabledRoutes = this.getEnabledRoutes();
    const disabledRoutes = this.getDisabledRoutes();

    return {
      routes: {
        total: this.routes.size,
        enabled: enabledRoutes.length,
        disabled: disabledRoutes.length
      },
      middleware: {
        total: this.middleware.size
      },
      routers: {
        total: this.routers.size
      },
      timestamp: new Date()
    };
  }

  clear() {
    this.routes.clear();
    this.middleware.clear();
    this.routers.clear();
    
    this.logger.info('Route registry cleared', {
      service: 'route-registry'
    });
  }
}

module.exports = RouteRegistry;
