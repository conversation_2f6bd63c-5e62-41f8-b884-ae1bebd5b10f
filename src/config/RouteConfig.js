class RouteConfig {
  constructor() {
    this.config = {
      // Core API routes configuration
      api: {
        enabled: process.env.API_ROUTES_ENABLED !== 'false',
        basePath: process.env.API_BASE_PATH || '/api',
        routes: {
          auth: {
            enabled: process.env.AUTH_ROUTES_ENABLED !== 'false',
            path: '/auth',
            endpoints: {
              status: {
                enabled: process.env.AUTH_STATUS_ENABLED !== 'false',
                path: '/status',
                method: 'GET',
                requireAuth: false,
                rateLimit: {
                  enabled: process.env.AUTH_STATUS_RATE_LIMIT_ENABLED === 'true',
                  windowMs: parseInt(process.env.AUTH_STATUS_RATE_LIMIT_WINDOW) || 60000,
                  max: parseInt(process.env.AUTH_STATUS_RATE_LIMIT_MAX) || 100
                }
              }
            }
          },
          stats: {
            enabled: process.env.STATS_ROUTES_ENABLED !== 'false',
            path: '/stats',
            method: 'GET',
            requireAuth: process.env.STATS_REQUIRE_AUTH === 'true',
            rateLimit: {
              enabled: process.env.STATS_RATE_LIMIT_ENABLED === 'true',
              windowMs: parseInt(process.env.STATS_RATE_LIMIT_WINDOW) || 60000,
              max: parseInt(process.env.STATS_RATE_LIMIT_MAX) || 50
            }
          },
          documents: {
            enabled: process.env.DOCUMENT_ROUTES_ENABLED !== 'false',
            basePath: '/documents',
            endpoints: {
              get: {
                enabled: process.env.DOCUMENT_GET_ENABLED !== 'false',
                path: '/:documentId',
                method: 'GET',
                requireAuth: process.env.DOCUMENT_GET_REQUIRE_AUTH === 'true'
              },
              delete: {
                enabled: process.env.DOCUMENT_DELETE_ENABLED !== 'false',
                path: '/:documentId',
                method: 'DELETE',
                requireAuth: process.env.DOCUMENT_DELETE_REQUIRE_AUTH !== 'false'
              }
            }
          },
          debug: {
            enabled: process.env.DEBUG_ROUTES_ENABLED === 'true',
            basePath: '/debug',
            requireAuth: process.env.DEBUG_REQUIRE_AUTH !== 'false',
            endpoints: {
              documents: {
                enabled: process.env.DEBUG_DOCUMENTS_ENABLED === 'true',
                path: '/documents',
                method: 'GET'
              }
            }
          }
        }
      },

      // Dashboard routes configuration
      dashboard: {
        enabled: process.env.DASHBOARD_ROUTES_ENABLED !== 'false',
        basePath: process.env.DASHBOARD_BASE_PATH || '/dashboard',
        requireAuth: process.env.DASHBOARD_REQUIRE_AUTH === 'true',
        routes: {
          metrics: {
            enabled: process.env.DASHBOARD_METRICS_ENABLED !== 'false',
            path: '/metrics',
            method: 'GET'
          },
          documents: {
            enabled: process.env.DASHBOARD_DOCUMENTS_ENABLED !== 'false',
            path: '/documents',
            method: 'GET'
          },
          health: {
            enabled: process.env.DASHBOARD_HEALTH_ENABLED !== 'false',
            path: '/health',
            method: 'GET'
          },
          performance: {
            enabled: process.env.DASHBOARD_PERFORMANCE_ENABLED !== 'false',
            path: '/performance',
            method: 'GET'
          }
        }
      },

      // Management routes configuration
      management: {
        enabled: process.env.MANAGEMENT_ROUTES_ENABLED === 'true',
        basePath: process.env.MANAGEMENT_BASE_PATH || '/manage',
        requireAuth: process.env.MANAGEMENT_REQUIRE_AUTH !== 'false',
        routes: {
          gc: {
            enabled: process.env.GC_ENDPOINT_ENABLED === 'true',
            path: '/gc',
            method: 'POST'
          },
          cleanup: {
            enabled: process.env.CLEANUP_ENDPOINTS_ENABLED === 'true',
            basePath: '/cleanup',
            endpoints: {
              idle: {
                enabled: process.env.CLEANUP_IDLE_ENABLED === 'true',
                path: '/idle',
                method: 'POST'
              },
              documents: {
                enabled: process.env.CLEANUP_DOCUMENTS_ENABLED === 'true',
                path: '/documents',
                method: 'POST'
              }
            }
          }
        }
      },

      // Health check routes
      health: {
        enabled: process.env.HEALTH_ROUTES_ENABLED !== 'false',
        path: process.env.HEALTH_PATH || '/health',
        method: 'GET',
        requireAuth: process.env.HEALTH_REQUIRE_AUTH === 'true'
      },

      // Static file serving
      static: {
        enabled: process.env.STATIC_FILES_ENABLED !== 'false',
        path: process.env.STATIC_PATH || '/public',
        directory: process.env.STATIC_DIRECTORY || 'public'
      },

      // UI routes (can be completely disabled for headless mode)
      ui: {
        enabled: process.env.UI_ROUTES_ENABLED !== 'false',
        routes: {
          dashboard: {
            enabled: process.env.UI_DASHBOARD_ENABLED !== 'false',
            path: process.env.UI_DASHBOARD_PATH || '/dashboard',
            file: 'dashboard.html'
          },
          root: {
            enabled: process.env.UI_ROOT_ENABLED !== 'false',
            path: '/',
            redirect: process.env.UI_ROOT_REDIRECT || null
          }
        }
      },

      // Global middleware configuration
      middleware: {
        cors: {
          enabled: process.env.CORS_ENABLED !== 'false',
          options: {
            origin: process.env.CORS_ORIGIN || '*',
            methods: (process.env.CORS_METHODS || 'GET,POST,PUT,DELETE').split(','),
            credentials: process.env.CORS_CREDENTIALS === 'true'
          }
        },
        helmet: {
          enabled: process.env.HELMET_ENABLED !== 'false',
          options: {}
        },
        rateLimit: {
          enabled: process.env.GLOBAL_RATE_LIMIT_ENABLED === 'true',
          windowMs: parseInt(process.env.GLOBAL_RATE_LIMIT_WINDOW) || 900000, // 15 minutes
          max: parseInt(process.env.GLOBAL_RATE_LIMIT_MAX) || 1000
        }
      }
    };
  }

  get(path) {
    return this.getNestedValue(this.config, path);
  }

  getNestedValue(obj, path) {
    if (!path) return obj;
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  isRouteEnabled(routePath) {
    const route = this.get(routePath);
    return route && route.enabled !== false;
  }

  getRouteConfig(routePath) {
    return this.get(routePath);
  }

  getAllEnabledRoutes() {
    const enabledRoutes = {};
    
    // Recursively find all enabled routes
    const findEnabledRoutes = (obj, currentPath = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const fullPath = currentPath ? `${currentPath}.${key}` : key;
        
        if (typeof value === 'object' && value !== null) {
          if (value.enabled === true || (value.enabled !== false && value.path)) {
            enabledRoutes[fullPath] = value;
          }
          findEnabledRoutes(value, fullPath);
        }
      }
    };

    findEnabledRoutes(this.config);
    return enabledRoutes;
  }

  validate() {
    const errors = [];

    // Validate required configurations
    if (!this.config.api.basePath) {
      errors.push('API base path is required');
    }

    if (this.config.static.enabled && !this.config.static.directory) {
      errors.push('Static directory is required when static files are enabled');
    }

    // Validate rate limit configurations
    const validateRateLimit = (config, path) => {
      if (config.rateLimit && config.rateLimit.enabled) {
        if (!config.rateLimit.windowMs || config.rateLimit.windowMs <= 0) {
          errors.push(`Invalid rate limit window for ${path}`);
        }
        if (!config.rateLimit.max || config.rateLimit.max <= 0) {
          errors.push(`Invalid rate limit max for ${path}`);
        }
      }
    };

    // Validate all route rate limits
    const validateRouteRateLimits = (obj, currentPath = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const fullPath = currentPath ? `${currentPath}.${key}` : key;
        
        if (typeof value === 'object' && value !== null) {
          if (value.rateLimit) {
            validateRateLimit(value, fullPath);
          }
          validateRouteRateLimits(value, fullPath);
        }
      }
    };

    validateRouteRateLimits(this.config);

    if (errors.length > 0) {
      throw new Error(`Route configuration validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  // Helper method to create a minimal configuration for headless mode
  static createHeadlessConfig() {
    const config = new RouteConfig();
    
    // Disable UI routes
    config.config.ui.enabled = false;
    config.config.dashboard.enabled = false;
    config.config.static.enabled = false;
    
    // Keep only essential API routes
    config.config.api.routes.debug.enabled = false;
    config.config.management.enabled = false;
    
    return config;
  }

  // Helper method to create a full-featured configuration
  static createFullConfig() {
    return new RouteConfig();
  }
}

module.exports = RouteConfig;
