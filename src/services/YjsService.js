const { setDocumentManager } = require('../utils/y-websocket-utils');

class YjsService {
  constructor(connectionManager, documentManager, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.logger = logger;
  }

  async initialize() {
    this.logger.info('YJS Service initializing...');
    setDocumentManager(this.documentManager);
    this.logger.info('Document manager connected to Y.js WebSocket utilities');

    this.setupPeriodicCleanup();
    this.logger.info('YJS Service initialized successfully');
  }

  setupPeriodicCleanup() {
    const cleanupInterval = 5 * 60 * 1000;

    setInterval(async () => {
      try {
        const cleanedCount = await this.documentManager.cleanup();
        if (cleanedCount > 0) {
          this.logger.info('Periodic cleanup completed', { cleanedCount });
        }
      } catch (error) {
        this.logger.error('Periodic cleanup failed, continuing operation', error);
        // Don't throw - just log and continue periodic cleanup
      }
    }, cleanupInterval);
  }

  getStats() {
    try {
      const connectionStats = this.connectionManager.getConnectionStats();
      const documentStats = this.documentManager.getOverallStats();

      return {
        connections: connectionStats,
        documents: documentStats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Failed to get service stats', error);
      throw error;
    }
  }

  getDocumentInfo(documentId) {
    try {
      const documentStats = this.documentManager.getDocumentStats(documentId);
      const connections = this.connectionManager.getConnectionsByDocument(documentId);

      return {
        documentId,
        stats: documentStats,
        connections: connections.map(conn => ({
          id: conn.id,
          userId: conn.userId,
          joinedAt: conn.joinedAt,
          lastActivity: conn.lastActivity
        }))
      };
    } catch (error) {
      this.logger.error('Failed to get document info', error, { documentId });
      throw error;
    }
  }

  cleanupDocument(documentId) {
    try {
      const connections = this.connectionManager.getConnectionsByDocument(documentId);
      
      if (connections.length > 0) {
        throw new Error('Cannot cleanup document with active connections');
      }

      const removed = this.documentManager.removeDocument(documentId);
      
      this.logger.info('Document cleanup forced', { documentId, removed });
      
      return removed;
    } catch (error) {
      this.logger.error('Failed to cleanup document', error, { documentId });
      throw error;
    }
  }

  /**
   * Handle graceful shutdown
   */
  async shutdown() {
    try {
      this.logger.info('YJS Service shutting down...');

      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
      }

      if (this.documentManager) {
        await this.documentManager.destroy();
      }

      if (this.connectionManager) {
        this.connectionManager.destroy();
      }

      // Notify all connected clients via WebSocket (if connectionManager exists)
      if (this.connectionManager && this.connectionManager.connections) {
        try {
          const allConnections = Array.from(this.connectionManager.connections.values());
          allConnections.forEach(connection => {
            if (connection.ws && connection.ws.readyState === 1) { // WebSocket.OPEN
              try {
                connection.ws.send(JSON.stringify({
                  type: 'server-shutdown',
                  message: 'Server is shutting down',
                  timestamp: new Date().toISOString()
                }));
              } catch (error) {
                this.logger.warn('Failed to notify client of shutdown', error, {
                  connectionId: connection.id
                });
              }
            }
          });
        } catch (error) {
          this.logger.warn('Failed to access connections during shutdown', error);
        }
      } else {
        this.logger.warn('ConnectionManager not available during shutdown');
      }

      // Give clients time to handle shutdown notification
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Cleanup resources
      await this.documentManager.destroy();

      this.logger.info('YJS Service shutdown completed');
    } catch (error) {
      this.logger.error('Error during YJS Service shutdown', error);
      throw error;
    }
  }

  /**
   * Health check for the service
   */
  healthCheck() {
    try {
      const stats = this.getStats();
      
      return {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = YjsService;
