const express = require('express');
const path = require('path');
const fs = require('fs');

class UIRouteHandler {
  constructor(logger, routeConfig) {
    this.logger = logger;
    this.routeConfig = routeConfig;
  }

  createUIRouter() {
    const uiRouter = express.Router();

    // Only create UI routes if UI is enabled
    if (!this.routeConfig.isRouteEnabled('ui')) {
      this.logger.info('UI routes disabled by configuration', {
        service: 'ui-route-handler'
      });
      return uiRouter;
    }

    this.setupDashboardRoute(uiRouter);
    this.setupRootRoute(uiRouter);
    this.setupStaticFileServing(uiRouter);

    this.logger.info('UI routes configured', {
      service: 'ui-route-handler'
    });

    return uiRouter;
  }

  setupDashboardRoute(router) {
    const dashboardConfig = this.routeConfig.getRouteConfig('ui.routes.dashboard');
    
    if (!dashboardConfig || !dashboardConfig.enabled) {
      this.logger.debug('Dashboard route disabled', {
        service: 'ui-route-handler'
      });
      return;
    }

    const dashboardPath = dashboardConfig.path || '/dashboard';
    const dashboardFile = dashboardConfig.file || 'dashboard.html';

    router.get(dashboardPath, (req, res) => {
      try {
        // Try multiple possible paths for the dashboard file
        const staticConfig = this.routeConfig.getRouteConfig('static');
        const staticDirectory = staticConfig?.directory || 'public';
        
        const possiblePaths = [
          path.join(process.cwd(), staticDirectory, dashboardFile),
          path.join(__dirname, '..', '..', staticDirectory, dashboardFile),
          path.join('/app', staticDirectory, dashboardFile)
        ];

        let filePath = null;
        
        for (const attemptPath of possiblePaths) {
          if (fs.existsSync(attemptPath)) {
            filePath = attemptPath;
            break;
          }
        }

        if (filePath) {
          res.sendFile(filePath);
          this.logger.debug('Dashboard served successfully', {
            path: dashboardPath,
            file: filePath,
            service: 'ui-route-handler'
          });
        } else {
          this.logger.error('Dashboard file not found', {
            attemptedPaths: possiblePaths,
            cwd: process.cwd(),
            service: 'ui-route-handler'
          });
          res.status(404).json({
            error: 'Dashboard not found',
            message: 'Dashboard file could not be located'
          });
        }
      } catch (error) {
        this.logger.error('Dashboard route error', error, {
          service: 'ui-route-handler'
        });
        res.status(500).json({
          error: 'Internal server error',
          message: 'Failed to serve dashboard'
        });
      }
    });

    this.logger.debug('Dashboard route configured', {
      path: dashboardPath,
      file: dashboardFile,
      service: 'ui-route-handler'
    });
  }

  setupRootRoute(router) {
    const rootConfig = this.routeConfig.getRouteConfig('ui.routes.root');
    
    if (!rootConfig || !rootConfig.enabled) {
      this.logger.debug('Root route disabled', {
        service: 'ui-route-handler'
      });
      return;
    }

    const rootPath = rootConfig.path || '/';

    router.get(rootPath, (req, res) => {
      try {
        if (rootConfig.redirect) {
          res.redirect(rootConfig.redirect);
          this.logger.debug('Root route redirected', {
            to: rootConfig.redirect,
            service: 'ui-route-handler'
          });
        } else {
          // Default behavior - serve a simple landing page or redirect to dashboard
          const dashboardConfig = this.routeConfig.getRouteConfig('ui.routes.dashboard');
          if (dashboardConfig && dashboardConfig.enabled) {
            res.redirect(dashboardConfig.path || '/dashboard');
          } else {
            res.json({
              message: 'Realtime YJS Server',
              status: 'running',
              timestamp: new Date().toISOString(),
              availableEndpoints: this.getAvailableEndpoints()
            });
          }
        }
      } catch (error) {
        this.logger.error('Root route error', error, {
          service: 'ui-route-handler'
        });
        res.status(500).json({
          error: 'Internal server error',
          message: 'Failed to serve root route'
        });
      }
    });

    this.logger.debug('Root route configured', {
      path: rootPath,
      redirect: rootConfig.redirect,
      service: 'ui-route-handler'
    });
  }

  setupStaticFileServing(router) {
    const staticConfig = this.routeConfig.getRouteConfig('static');
    
    if (!staticConfig || !staticConfig.enabled) {
      this.logger.debug('Static file serving disabled', {
        service: 'ui-route-handler'
      });
      return;
    }

    const staticPath = staticConfig.path || '/public';
    const staticDirectory = staticConfig.directory || 'public';

    // Serve static files
    router.use(staticPath, express.static(staticDirectory));

    this.logger.debug('Static file serving configured', {
      path: staticPath,
      directory: staticDirectory,
      service: 'ui-route-handler'
    });
  }

  getAvailableEndpoints() {
    const endpoints = [];

    // Add API endpoints if enabled
    if (this.routeConfig.isRouteEnabled('api')) {
      const apiBasePath = this.routeConfig.getRouteConfig('api.basePath') || '/api';
      endpoints.push(`GET ${apiBasePath}/stats - Server statistics`);
      endpoints.push(`GET ${apiBasePath}/auth/status - Authentication status`);
      
      if (this.routeConfig.isRouteEnabled('api.routes.documents')) {
        endpoints.push(`GET ${apiBasePath}/documents/:id - Document information`);
        endpoints.push(`DELETE ${apiBasePath}/documents/:id - Delete document`);
      }
    }

    // Add dashboard endpoints if enabled
    if (this.routeConfig.isRouteEnabled('dashboard')) {
      const dashboardBasePath = this.routeConfig.getRouteConfig('dashboard.basePath') || '/dashboard';
      endpoints.push(`GET ${dashboardBasePath}/metrics - Dashboard metrics`);
      endpoints.push(`GET ${dashboardBasePath}/documents - Document details`);
      endpoints.push(`GET ${dashboardBasePath}/health - System health`);
      endpoints.push(`GET ${dashboardBasePath}/performance - Performance metrics`);
    }

    // Add health endpoint if enabled
    if (this.routeConfig.isRouteEnabled('health')) {
      const healthPath = this.routeConfig.getRouteConfig('health.path') || '/health';
      endpoints.push(`GET ${healthPath} - Health check`);
    }

    return endpoints;
  }

  createDeprecatedRoutesHandler() {
    return (req, res) => {
      res.status(404).json({
        error: 'Examples Removed',
        message: 'Old example files have been removed. Please use the main Tiptap collaborative editor at /',
        redirect: '/'
      });
    };
  }
}

module.exports = UIRouteHandler;
