const express = require('express');
const RouteRegistry = require('../registry/RouteRegistry');
const MiddlewareFactory = require('../middleware/MiddlewareFactory');

class ConfigurableRouteHandler {
  constructor(logger, authenticationHandler, routeConfig) {
    this.logger = logger;
    this.authenticationHandler = authenticationHandler;
    this.routeConfig = routeConfig;
    this.yjsService = null;
    
    // Initialize route registry and middleware factory
    this.routeRegistry = new RouteRegistry(logger);
    this.middlewareFactory = new MiddlewareFactory(logger, authenticationHandler);
    
    // Register common middleware
    this.registerCommonMiddleware();
  }

  setYjsService(yjsService) {
    this.yjsService = yjsService;
  }

  registerCommonMiddleware() {
    // Register authentication middleware
    this.routeRegistry.registerMiddleware('auth-required', 
      this.middlewareFactory.createAuthMiddleware({ required: true }));
    
    this.routeRegistry.registerMiddleware('auth-optional', 
      this.middlewareFactory.createAuthMiddleware({ required: false }));

    // Register rate limiting middleware
    const globalRateLimit = this.routeConfig.getRouteConfig('middleware.rateLimit');
    if (globalRateLimit && globalRateLimit.enabled) {
      this.routeRegistry.registerMiddleware('global-rate-limit', 
        this.middlewareFactory.createRateLimitMiddleware(globalRateLimit));
    }

    // Register logging middleware
    this.routeRegistry.registerMiddleware('request-logging', 
      this.middlewareFactory.createLoggingMiddleware());

    // Register validation middleware
    this.routeRegistry.registerMiddleware('validate-document-id', 
      this.middlewareFactory.createValidationMiddleware({
        requiredParams: ['documentId']
      }));

    this.logger.debug('Common middleware registered', {
      service: 'configurable-route-handler'
    });
  }

  createApiRouter() {
    // Check if API routes are enabled
    if (!this.routeConfig.isRouteEnabled('api')) {
      this.logger.info('API routes disabled by configuration', {
        service: 'configurable-route-handler'
      });
      return express.Router();
    }

    const apiRouter = express.Router();

    // Register all API routes based on configuration
    this.registerAuthRoutes(apiRouter);
    this.registerStatsRoutes(apiRouter);
    this.registerDocumentRoutes(apiRouter);
    this.registerDebugRoutes(apiRouter);
    this.registerDashboardRoutes(apiRouter);
    this.registerManagementRoutes(apiRouter);

    this.logger.info('API routes configured', {
      service: 'configurable-route-handler'
    });

    return apiRouter;
  }

  registerAuthRoutes(router) {
    const authConfig = this.routeConfig.getRouteConfig('api.routes.auth');
    if (!authConfig || !authConfig.enabled) {
      return;
    }

    const authBasePath = authConfig.path || '/auth';
    
    // Auth status endpoint
    const statusConfig = authConfig.endpoints?.status;
    if (statusConfig && statusConfig.enabled) {
      const statusPath = `${authBasePath}${statusConfig.path || '/status'}`;
      
      router.get(statusPath, async (req, res) => {
        try {
          const token = req.headers.authorization;
          if (!token) {
            return res.status(401).json({ authenticated: false, error: 'No token provided' });
          }
          
          const authMiddleware = this.authenticationHandler.getAuthMiddleware();
          const userInfo = await authMiddleware.validateToken(token);
          if (!userInfo) {
            return res.status(401).json({ authenticated: false, error: 'Invalid token' });
          }

          res.json({
            authenticated: true,
            user: {
              id: userInfo.userId,
              username: userInfo.username,
              email: userInfo.email
            }
          });
        } catch (error) {
          this.logger.error('Auth status check failed', error, {
            service: 'configurable-route-handler'
          });
          res.status(500).json({ authenticated: false, error: 'Internal server error' });
        }
      });
    }
  }

  registerStatsRoutes(router) {
    const statsConfig = this.routeConfig.getRouteConfig('api.routes.stats');
    if (!statsConfig || !statsConfig.enabled) {
      return;
    }

    const statsPath = statsConfig.path || '/stats';
    const middleware = [];
    
    if (statsConfig.requireAuth) {
      middleware.push(this.routeRegistry.getMiddleware('auth-required'));
    }

    router.get(statsPath, ...middleware, (req, res) => {
      try {
        if (this.yjsService) {
          const stats = this.yjsService.getStats();
          res.json(stats);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get stats', error, {
          service: 'configurable-route-handler'
        });
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  }

  registerDocumentRoutes(router) {
    const documentsConfig = this.routeConfig.getRouteConfig('api.routes.documents');
    if (!documentsConfig || !documentsConfig.enabled) {
      return;
    }

    const documentsBasePath = documentsConfig.basePath || '/documents';
    
    // Document GET endpoint
    const getConfig = documentsConfig.endpoints?.get;
    if (getConfig && getConfig.enabled) {
      const getPath = `${documentsBasePath}${getConfig.path || '/:documentId'}`;
      const middleware = [];
      
      if (getConfig.requireAuth) {
        middleware.push(this.routeRegistry.getMiddleware('auth-required'));
      }
      middleware.push(this.routeRegistry.getMiddleware('validate-document-id'));

      router.get(getPath, ...middleware, (req, res) => {
        try {
          const { documentId } = req.params;
          if (this.yjsService) {
            const info = this.yjsService.getDocumentInfo(documentId);
            res.json(info);
          } else {
            res.status(503).json({ error: 'YJS service not available' });
          }
        } catch (error) {
          this.logger.error('Failed to get document info', error, {
            documentId: req.params.documentId,
            service: 'configurable-route-handler'
          });
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    }

    // Document DELETE endpoint
    const deleteConfig = documentsConfig.endpoints?.delete;
    if (deleteConfig && deleteConfig.enabled) {
      const deletePath = `${documentsBasePath}${deleteConfig.path || '/:documentId'}`;
      const middleware = [];
      
      if (deleteConfig.requireAuth) {
        middleware.push(this.routeRegistry.getMiddleware('auth-required'));
      }
      middleware.push(this.routeRegistry.getMiddleware('validate-document-id'));

      router.delete(deletePath, ...middleware, (req, res) => {
        try {
          const { documentId } = req.params;
          if (this.yjsService) {
            const removed = this.yjsService.cleanupDocument(documentId);
            res.json({ removed, documentId });
          } else {
            res.status(503).json({ error: 'YJS service not available' });
          }
        } catch (error) {
          this.logger.error('Failed to cleanup document', error, {
            documentId: req.params.documentId,
            service: 'configurable-route-handler'
          });
          res.status(400).json({ error: error.message });
        }
      });
    }
  }

  registerDebugRoutes(router) {
    const debugConfig = this.routeConfig.getRouteConfig('api.routes.debug');
    if (!debugConfig || !debugConfig.enabled) {
      return;
    }

    const debugBasePath = debugConfig.basePath || '/debug';
    const middleware = [];
    
    if (debugConfig.requireAuth) {
      middleware.push(this.routeRegistry.getMiddleware('auth-required'));
    }

    // Debug documents endpoint
    const documentsConfig = debugConfig.endpoints?.documents;
    if (documentsConfig && documentsConfig.enabled) {
      const documentsPath = `${debugBasePath}${documentsConfig.path || '/documents'}`;
      
      router.get(documentsPath, ...middleware, (req, res) => {
        try {
          const { docs } = require('../utils/y-websocket-utils');
          const documentList = [];

          docs.forEach((doc, documentId) => {
            documentList.push({
              documentId,
              connectionCount: doc.conns.size,
              awarenessStates: doc.awareness.getStates().size,
              hasDoc: !!doc
            });
          });

          res.json({
            totalDocuments: docs.size,
            documents: documentList
          });
        } catch (error) {
          this.logger.error('Failed to get debug documents', error, {
            service: 'configurable-route-handler'
          });
          res.status(500).json({ error: 'Internal server error' });
        }
      });
    }
  }

  registerDashboardRoutes(router) {
    const dashboardConfig = this.routeConfig.getRouteConfig('dashboard');
    if (!dashboardConfig || !dashboardConfig.enabled) {
      return;
    }

    const dashboardBasePath = dashboardConfig.basePath || '/dashboard';
    const middleware = [];

    if (dashboardConfig.requireAuth) {
      middleware.push(this.routeRegistry.getMiddleware('auth-required'));
    }

    // Dashboard metrics endpoint
    const metricsConfig = dashboardConfig.routes?.metrics;
    if (metricsConfig && metricsConfig.enabled) {
      const metricsPath = `${dashboardBasePath}${metricsConfig.path || '/metrics'}`;

      router.get(metricsPath, ...middleware, (req, res) => {
        try {
          const memUsage = process.memoryUsage();
          const { docs } = require('../utils/y-websocket-utils');

          let totalConnections = 0;
          let totalDocumentSize = 0;
          const documentMetrics = [];

          docs.forEach((doc, documentId) => {
            const connections = doc.conns.size;
            totalConnections += connections;

            let docSize = 0;
            try {
              const { getDocumentStateSize } = require('../utils/y-websocket-utils');
              docSize = getDocumentStateSize(doc);
              totalDocumentSize += docSize;
            } catch (error) {
              // Ignore size calculation errors
            }

            documentMetrics.push({
              id: documentId,
              connections,
              size: docSize,
              lastActivity: doc.lastActivity || Date.now(),
              isActive: connections > 0
            });
          });

          const metrics = {
            timestamp: Date.now(),
            server: {
              uptime: Math.round(process.uptime()),
              nodeVersion: process.version,
              platform: process.platform,
              pid: process.pid
            },
            memory: {
              heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
              heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
              heapUsagePercent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
              rss: Math.round(memUsage.rss / 1024 / 1024),
              external: Math.round(memUsage.external / 1024 / 1024),
              arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024)
            },
            documents: {
              total: docs.size,
              active: documentMetrics.filter(d => d.isActive).length,
              totalSize: Math.round(totalDocumentSize / 1024),
              list: documentMetrics.slice(0, 10)
            },
            connections: {
              total: totalConnections,
              averagePerDocument: docs.size > 0 ? Math.round(totalConnections / docs.size * 100) / 100 : 0
            }
          };

          if (this.yjsService && this.yjsService.documentManager) {
            const dm = this.yjsService.documentManager;
            if (dm.memoryManager) {
              metrics.optimization = {
                memoryManager: dm.memoryManager.getMemoryStats(),
                performanceMonitor: dm.performanceMonitor ? dm.performanceMonitor.getPerformanceSummary() : null
              };
            }
          }

          res.json(metrics);
        } catch (error) {
          this.logger.error('Failed to get dashboard metrics', error, {
            service: 'configurable-route-handler'
          });
          res.status(500).json({ error: 'Failed to get dashboard metrics' });
        }
      });
    }

    // Dashboard health endpoint
    const healthConfig = dashboardConfig.routes?.health;
    if (healthConfig && healthConfig.enabled) {
      const healthPath = `${dashboardBasePath}${healthConfig.path || '/health'}`;

      router.get(healthPath, ...middleware, async (req, res) => {
        try {
          const health = {
            timestamp: Date.now(),
            status: 'healthy',
            checks: {
              server: { status: 'healthy', uptime: Math.round(process.uptime()) },
              memory: { status: 'healthy', usage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100) },
              redis: { status: 'unknown', connected: false },
              websocket: { status: 'healthy', connections: 0 }
            }
          };

          // Check Redis connection
          if (this.yjsService && this.yjsService.documentManager && this.yjsService.documentManager.redisSync) {
            try {
              const redisSync = this.yjsService.documentManager.redisSync;
              health.checks.redis = {
                status: 'healthy',
                connected: true,
                metrics: redisSync.getMetrics()
              };
            } catch (error) {
              health.checks.redis = { status: 'error', connected: false, error: error.message };
            }
          }

          // Check WebSocket connections
          const { docs } = require('../utils/y-websocket-utils');
          let totalConnections = 0;
          docs.forEach(doc => totalConnections += doc.conns.size);
          health.checks.websocket.connections = totalConnections;

          // Check memory usage
          const memUsage = (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100;
          if (memUsage > 90) {
            health.checks.memory.status = 'warning';
            health.status = 'warning';
          } else if (memUsage > 95) {
            health.checks.memory.status = 'critical';
            health.status = 'critical';
          }

          res.json(health);
        } catch (error) {
          this.logger.error('Failed to get system health', error, {
            service: 'configurable-route-handler'
          });
          res.status(500).json({
            timestamp: Date.now(),
            status: 'error',
            error: 'Failed to get system health'
          });
        }
      });
    }
  }

  registerManagementRoutes(router) {
    const managementConfig = this.routeConfig.getRouteConfig('management');
    if (!managementConfig || !managementConfig.enabled) {
      return;
    }

    const managementBasePath = managementConfig.basePath || '/manage';
    const middleware = [];

    if (managementConfig.requireAuth) {
      middleware.push(this.routeRegistry.getMiddleware('auth-required'));
    }

    // Garbage collection endpoint
    const gcConfig = managementConfig.routes?.gc;
    if (gcConfig && gcConfig.enabled) {
      const gcPath = `${managementBasePath}${gcConfig.path || '/gc'}`;

      router.post(gcPath, ...middleware, (req, res) => {
        try {
          if (global.gc) {
            const beforeGC = process.memoryUsage();
            global.gc();
            const afterGC = process.memoryUsage();

            const improvement = {
              heapUsedBefore: Math.round(beforeGC.heapUsed / 1024 / 1024),
              heapUsedAfter: Math.round(afterGC.heapUsed / 1024 / 1024),
              heapFreed: Math.round((beforeGC.heapUsed - afterGC.heapUsed) / 1024 / 1024),
              rssFreed: Math.round((beforeGC.rss - afterGC.rss) / 1024 / 1024)
            };

            this.logger.info('Manual garbage collection triggered', improvement);

            res.json({
              success: true,
              message: 'Garbage collection completed',
              improvement,
              timestamp: new Date().toISOString()
            });
          } else {
            res.status(400).json({
              success: false,
              error: 'Garbage collection not available. Start with --expose-gc flag.'
            });
          }
        } catch (error) {
          this.logger.error('Failed to trigger GC', error);
          res.status(500).json({
            success: false,
            error: 'Failed to trigger garbage collection'
          });
        }
      });
    }
  }

  createHealthRoute(wss) {
    if (!this.routeConfig.isRouteEnabled('health')) {
      return (req, res) => {
        res.status(404).json({ error: 'Health endpoint disabled' });
      };
    }

    return async (req, res) => {
      try {
        const health = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          websocket: wss ? 'active' : 'inactive'
        };

        if (this.yjsService && this.yjsService.documentManager && this.yjsService.documentManager.redisSync) {
          const redisHealth = await this.yjsService.documentManager.redisSync.healthCheck();
          health.redisSync = redisHealth;
        }

        res.json(health);
      } catch (error) {
        this.logger.error('Health check failed', error, {
          service: 'configurable-route-handler'
        });
        res.status(500).json({
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    };
  }

  createDeprecatedRoutesHandler() {
    return (req, res) => {
      res.status(404).json({
        error: 'Examples Removed',
        message: 'Old example files have been removed. Please use the main Tiptap collaborative editor at /',
        redirect: '/'
      });
    };
  }
}

module.exports = ConfigurableRouteHandler;
