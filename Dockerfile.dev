# Development Dockerfile with hot reloading support
FROM node:22.7.0-alpine

# Set working directory in container
WORKDIR /app

# Create logs directory
RUN mkdir -p logs

# Copy package files first for better caching
COPY package*.json ./

# Install ALL dependencies (including devDependencies for development)
RUN npm ci && npm cache clean --force

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of app directory to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose ports
EXPOSE 3000
EXPOSE 9229

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { \
    if (res.statusCode === 200) process.exit(0); else process.exit(1); \
  }).on('error', () => process.exit(1));"

# Copy startup script
COPY --chown=nodejs:nodejs docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Use startup script that handles debug mode
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
