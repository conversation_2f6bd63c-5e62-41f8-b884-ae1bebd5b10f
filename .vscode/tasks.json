{"version": "2.0.0", "tasks": [{"label": "Start Debug Server", "type": "shell", "command": "make", "args": ["vscodedebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Start the Node.js server in debug mode with Docker"}, {"label": "Stop Debug Server", "type": "shell", "command": "make", "args": ["stop"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Stop the Docker debug services"}, {"label": "Build Development", "type": "shell", "command": "make", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Build the development Docker services"}]}