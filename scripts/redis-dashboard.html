<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis Sync Dashboard - Real-time Monitoring</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .status-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .status-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-connected {
            background: #27ae60;
        }
        
        .status-disconnected {
            background: #e74c3c;
        }
        
        .activity-log {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-log h3 {
            margin-bottom: 20px;
            color: #3498db;
        }
        
        .log-entry {
            padding: 8px 0;
            border-bottom: 1px solid #34495e;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .timestamp {
            color: #95a5a6;
            margin-right: 10px;
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .auto-refresh {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🔴 Redis Sync Dashboard</h1>
            <p>Real-time monitoring of YJS Redis Pub/Sub activity</p>
        </div>
        
        <div class="content">
            <div class="auto-refresh">
                <button class="refresh-btn" onclick="toggleAutoRefresh()">
                    <span id="auto-refresh-text">Start Auto Refresh</span>
                </button>
                <button class="refresh-btn" onclick="refreshData()">Refresh Now</button>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="messages-sent">-</div>
                    <div class="metric-label">Messages Sent</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="messages-received">-</div>
                    <div class="metric-label">Messages Received</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="documents-tracked">-</div>
                    <div class="metric-label">Documents Tracked</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="active-subscriptions">-</div>
                    <div class="metric-label">Active Subscriptions</div>
                </div>
            </div>
            
            <div class="status-section">
                <h3>🔗 Connection Status</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-indicator" id="server-status"></div>
                        <span>Server Health</span>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator" id="redis-publisher-status"></div>
                        <span>Redis Publisher</span>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator" id="redis-subscriber-status"></div>
                        <span>Redis Subscriber</span>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator" id="sync-status"></div>
                        <span>Document Sync</span>
                    </div>
                </div>
            </div>
            
            <div class="status-section">
                <h3>📊 Instance Information</h3>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <p><strong>Instance ID:</strong> <span id="instance-id">-</span></p>
                    <p><strong>Last Activity:</strong> <span id="last-activity">-</span></p>
                    <p><strong>Total Documents:</strong> <span id="total-documents">-</span></p>
                    <p><strong>Total Connections:</strong> <span id="total-connections">-</span></p>
                </div>
            </div>
            
            <div class="activity-log">
                <h3>📝 Activity Log</h3>
                <div id="activity-log-content">
                    <div class="loading">Loading activity data...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;
        
        async function fetchServerData() {
            try {
                const [healthResponse, statsResponse] = await Promise.all([
                    fetch('http://localhost:3000/health'),
                    fetch('http://localhost:3000/api/stats')
                ]);
                
                const health = await healthResponse.json();
                const stats = await statsResponse.json();
                
                return { health, stats };
            } catch (error) {
                console.error('Error fetching data:', error);
                throw error;
            }
        }
        
        function updateMetrics(data) {
            const redisSync = data.stats.documents.redisSync;
            
            document.getElementById('messages-sent').textContent = redisSync.messagesSent;
            document.getElementById('messages-received').textContent = redisSync.messagesReceived;
            document.getElementById('documents-tracked').textContent = redisSync.documentsTracked;
            document.getElementById('active-subscriptions').textContent = redisSync.activeSubscriptions;
        }
        
        function updateStatus(data) {
            const serverStatus = data.health.status === 'healthy';
            const redisPublisher = data.health.redisSync?.redis?.publisher === 'connected';
            const redisSubscriber = data.health.redisSync?.redis?.subscriber === 'connected';
            const syncStatus = data.health.redisSync?.status === 'healthy';
            
            document.getElementById('server-status').className = 
                `status-indicator ${serverStatus ? 'status-connected' : 'status-disconnected'}`;
            document.getElementById('redis-publisher-status').className = 
                `status-indicator ${redisPublisher ? 'status-connected' : 'status-disconnected'}`;
            document.getElementById('redis-subscriber-status').className = 
                `status-indicator ${redisSubscriber ? 'status-connected' : 'status-disconnected'}`;
            document.getElementById('sync-status').className = 
                `status-indicator ${syncStatus ? 'status-connected' : 'status-disconnected'}`;
        }
        
        function updateInstanceInfo(data) {
            document.getElementById('instance-id').textContent = 
                data.health.redisSync?.instanceId || 'Unknown';
            document.getElementById('last-activity').textContent = 
                new Date(data.stats.documents.redisSync.lastActivity).toLocaleString();
            document.getElementById('total-documents').textContent = 
                data.stats.documents.totalDocuments;
            document.getElementById('total-connections').textContent = 
                data.stats.documents.totalConnections;
        }
        
        function addLogEntry(message) {
            const logContent = document.getElementById('activity-log-content');
            const timestamp = new Date().toLocaleTimeString();
            
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span>${message}`;
            
            logContent.insertBefore(entry, logContent.firstChild);
            
            // Keep only last 20 entries
            while (logContent.children.length > 20) {
                logContent.removeChild(logContent.lastChild);
            }
        }
        
        async function refreshData() {
            try {
                const data = await fetchServerData();
                
                updateMetrics(data);
                updateStatus(data);
                updateInstanceInfo(data);
                
                addLogEntry(`Data refreshed - ${data.stats.documents.redisSync.activeSubscriptions} active subscriptions`);
                
            } catch (error) {
                addLogEntry(`❌ Error: ${error.message}`);
                document.getElementById('activity-log-content').innerHTML = 
                    '<div class="error">Failed to fetch server data. Make sure the server is running on localhost:3000</div>';
            }
        }
        
        function toggleAutoRefresh() {
            const button = document.getElementById('auto-refresh-text');
            
            if (isAutoRefreshing) {
                clearInterval(autoRefreshInterval);
                button.textContent = 'Start Auto Refresh';
                isAutoRefreshing = false;
                addLogEntry('Auto refresh stopped');
            } else {
                autoRefreshInterval = setInterval(refreshData, 2000); // Refresh every 2 seconds
                button.textContent = 'Stop Auto Refresh';
                isAutoRefreshing = true;
                addLogEntry('Auto refresh started (2s interval)');
                refreshData(); // Initial refresh
            }
        }
        
        // Initial load
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            addLogEntry('Dashboard initialized');
        });
    </script>
</body>
</html>
